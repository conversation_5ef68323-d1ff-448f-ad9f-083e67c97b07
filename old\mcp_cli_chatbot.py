#!/usr/bin/env python3
"""
MCP CLI Chatbot
Command-line interface for MCP hybrid memory medical assistant.
"""

import asyncio
import re
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass

# Import MCP Client Adapter
from Langgraph.mcp_client_adapter import MC<PERSON><PERSON>Adapter, ExtractedMemoryInfo

# Import LLM
from langchain_groq import ChatGroq
import os
from dotenv import load_dotenv

load_dotenv()

class MCPCLIChatbot:
    """MCP-enabled CLI chatbot with hybrid memory"""
    
    def __init__(self):
        print("🤖 Initializing MCP Hybrid Memory CLI Chatbot...")
        self.llm = self._init_llm()
        self.patient_name = ""
        self.conversation_history = []
        self.has_name = False
        self.mcp_client = None
        print("✅ MCP CLI Chatbot ready!")
        
    def _init_llm(self):
        """Initialize Groq LLM"""
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            raise ValueError("GROQ_API_KEY not found in .env file!")
        
        return ChatGroq(
            model="llama-3.1-8b-instant",
            api_key=groq_api_key,
            temperature=0.7
        )

    def extract_patient_name(self, text: str) -> bool:
        """Extract patient name from text"""
        if self.has_name:
            return True
            
        patterns = [
            r"(?:my name is|i'm|i am|call me)\s+([A-Za-z\s]+?)(?:\s*$|,|\.|!)",
            r"^([A-Za-z\s]+?)(?:\s+here|$|,|\.|!)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.strip(), re.IGNORECASE)
            if match:
                name = match.group(1).strip().title()
                if (name.lower() not in ['hello', 'hi', 'hey', 'good', 'there'] and 
                    len(name) > 2):
                    self.patient_name = name
                    self.has_name = True
                    print(f"👤 Patient name: {self.patient_name}")
                    return True
        return False

    async def extract_information_with_llm(self, text: str) -> ExtractedMemoryInfo:
        """Use LLM to intelligently extract and categorize medical information"""
        info = ExtractedMemoryInfo()
        
        if not self.has_name:
            return info
        
        # Use LLM to analyze and categorize the text
        analysis_prompt = f"""
        Analyze this patient statement and extract medical information. Categorize each piece of information:

        Patient statement: "{text}"

        Extract and categorize information into these categories:

        MEDICAL CONDITIONS: Any diseases, disorders, or chronic conditions (diabetes, asthma, hypertension, arthritis, etc.)
        
        ALLERGIES: Any allergies, intolerances, or adverse reactions (food allergies, drug allergies, environmental allergies like dust, pollen, etc.)
        
        MEDICATIONS: Any medications, drugs, treatments, or supplements being taken
        
        FAMILY HISTORY: Any family medical history mentioned
        
        CURRENT SYMPTOMS: Current health complaints, symptoms, or problems happening now (pain, headache, nausea, etc.)
        
        PAIN LEVEL: Any pain scale ratings (1-10 scale, mild/moderate/severe)
        
        APPOINTMENT PREFERENCES: Appointment time preferences (morning, afternoon, evening, weekday, weekend)
        
        COMMUNICATION PREFERENCES: How they prefer to be contacted (phone, email, text, etc.)
        
        LIFESTYLE HABITS: Exercise habits, diet, sleep patterns, work schedule, etc.

        Respond in this exact format:
        MEDICAL CONDITIONS: [list items separated by semicolons, or "none"]
        ALLERGIES: [list items separated by semicolons, or "none"]  
        MEDICATIONS: [list items separated by semicolons, or "none"]
        FAMILY HISTORY: [list items separated by semicolons, or "none"]
        CURRENT SYMPTOMS: [list items separated by semicolons, or "none"]
        PAIN LEVEL: [rating or "none"]
        APPOINTMENT PREFERENCES: [list items separated by semicolons, or "none"]
        COMMUNICATION PREFERENCES: [list items separated by semicolons, or "none"]
        LIFESTYLE HABITS: [list items separated by semicolons, or "none"]

        Examples:
        - "I have dust allergy" → ALLERGIES: dust
        - "I'm allergic to shellfish" → ALLERGIES: shellfish
        - "My back hurts" → CURRENT SYMPTOMS: back pain
        - "I take pills for my heart" → MEDICATIONS: heart medication
        - "I prefer morning appointments" → APPOINTMENT PREFERENCES: morning appointments
        """
        
        try:
            response = self.llm.invoke(analysis_prompt).content
            
            # Parse the LLM response
            lines = response.strip().split('\n')
            
            for line in lines:
                if ':' in line:
                    category, items = line.split(':', 1)
                    category = category.strip().upper()
                    items = items.strip()
                    
                    if items.lower() != 'none' and items:
                        item_list = [item.strip() for item in items.split(';') if item.strip()]
                        
                        if 'MEDICAL CONDITIONS' in category:
                            info.medical_conditions.extend(item_list)
                        elif 'ALLERGIES' in category:
                            info.allergies.extend(item_list)
                        elif 'MEDICATIONS' in category:
                            info.medications.extend(item_list)
                        elif 'FAMILY HISTORY' in category:
                            info.family_history.extend(item_list)
                        elif 'CURRENT SYMPTOMS' in category:
                            info.current_symptoms.extend(item_list)
                        elif 'PAIN LEVEL' in category:
                            info.pain_level = items
                        elif 'APPOINTMENT PREFERENCES' in category:
                            info.appointment_preferences.extend(item_list)
                        elif 'COMMUNICATION PREFERENCES' in category:
                            info.communication_preferences.extend(item_list)
                        elif 'LIFESTYLE HABITS' in category:
                            info.lifestyle_habits.extend(item_list)
            
            # Print what was extracted for debugging
            if any([info.medical_conditions, info.allergies, info.medications, info.family_history,
                   info.current_symptoms, info.pain_level, info.appointment_preferences,
                   info.communication_preferences, info.lifestyle_habits]):
                extracted_items = []
                if info.medical_conditions:
                    extracted_items.append(f"conditions: {len(info.medical_conditions)}")
                if info.allergies:
                    extracted_items.append(f"allergies: {len(info.allergies)}")
                if info.medications:
                    extracted_items.append(f"medications: {len(info.medications)}")
                if info.family_history:
                    extracted_items.append(f"family history: {len(info.family_history)}")
                if info.current_symptoms:
                    extracted_items.append(f"symptoms: {len(info.current_symptoms)}")
                if info.pain_level:
                    extracted_items.append("pain level")
                if info.appointment_preferences:
                    extracted_items.append(f"appointment prefs: {len(info.appointment_preferences)}")
                if info.communication_preferences:
                    extracted_items.append(f"communication prefs: {len(info.communication_preferences)}")
                if info.lifestyle_habits:
                    extracted_items.append(f"lifestyle: {len(info.lifestyle_habits)}")
                
                print(f"🤖 MCP Extracted: {', '.join(extracted_items)}")
        
        except Exception as e:
            print(f"❌ Error in LLM extraction: {e}")
        
        return info

    async def get_patient_context(self) -> str:
        """Get comprehensive patient context from MCP services"""
        if not self.patient_name or not self.mcp_client:
            return ""
        
        try:
            context = await self.mcp_client.get_comprehensive_patient_context(self.patient_name)
            return context
        except Exception as e:
            return f"Error retrieving patient context: {e}"

    async def generate_response(self, user_input: str) -> str:
        """Generate natural response using MCP context"""
        
        if not self.has_name:
            prompt = """
            You are a friendly medical assistant. The user just started talking to you.
            Greet them warmly and ask for their name in a natural way.
            Be professional but approachable.
            """
        else:
            context = await self.get_patient_context()
            
            prompt = f"""
            You are a caring medical assistant talking to {self.patient_name}.
            
            PATIENT CONTEXT FROM MCP SERVICES:
            {context}
            
            The patient just said: "{user_input}"
            
            Respond naturally and professionally:
            - If you recognize them from their medical history, acknowledge it warmly
            - If they mention symptoms, relate to their previous visits when relevant
            - If they mention new symptoms, ask appropriate follow-up questions
            - Reference their known conditions, allergies, and medications when relevant
            - Be empathetic and show continuity of care
            - Keep responses conversational and helpful
            
            Show that you know their medical history and care about their ongoing health.
            """
        
        try:
            response = self.llm.invoke(prompt).content
            return response
        except Exception as e:
            return f"I apologize, I'm having trouble right now. Could you please try again?"

    async def process_message(self, user_input: str) -> str:
        """Main message processing with MCP integration"""
        
        # Initialize MCP client if not already done
        if not self.mcp_client:
            self.mcp_client = MCPClientAdapter()
            await self.mcp_client.__aenter__()
        
        # Extract name if we don't have it
        if not self.has_name:
            self.extract_patient_name(user_input)
        
        # Extract information using LLM intelligence
        extracted_info = await self.extract_information_with_llm(user_input)
        
        # Store information in MCP memory systems
        if self.has_name and self.mcp_client:
            # Set visit date
            extracted_info.visit_date = datetime.now().strftime("%Y-%m-%d")
            
            # Store in all memory systems via MCP
            storage_results = await self.mcp_client.store_all_memories(self.patient_name, extracted_info)
            
            # Log storage results
            for memory_type, success in storage_results.items():
                if success:
                    print(f"✅ MCP {memory_type} storage: SUCCESS")
                else:
                    print(f"❌ MCP {memory_type} storage: FAILED")
        
        # Generate response with MCP context
        response = await self.generate_response(user_input)
        
        # Store conversation
        self.conversation_history.append({
            "user": user_input,
            "assistant": response,
            "timestamp": datetime.now().isoformat(),
            "extracted_info": extracted_info
        })
        
        return response

    async def cleanup(self):
        """Cleanup MCP client"""
        if self.mcp_client:
            await self.mcp_client.__aexit__(None, None, None)

    def get_session_summary(self) -> Dict:
        """Get session summary"""
        return {
            "patient_name": self.patient_name,
            "has_name": self.has_name,
            "total_exchanges": len(self.conversation_history),
            "mcp_enabled": self.mcp_client is not None,
            "memory_systems_used": ["episodic (MCP)", "semantic (Qdrant)", "behavioral (MCP)"]
        }

    def display_patient_info(self):
        """Display current patient information in CLI"""
        if self.has_name:
            print("\n" + "="*50)
            print(f"👤 PATIENT: {self.patient_name}")
            print(f"💬 EXCHANGES: {len(self.conversation_history)}")
            print(f"🔗 MCP STATUS: {'Active' if self.mcp_client else 'Inactive'}")
            print(f"🧠 MEMORY SYSTEMS: Episodic, Semantic (Qdrant), Behavioral")
            print("="*50)

async def main():
    """Main CLI chat loop with MCP integration"""
    print("🔗 MCP Hybrid Memory Medical Assistant (CLI)")
    print("=" * 50)
    print("Hello! I'm your medical assistant with MCP-powered memory.")
    print("I use Qdrant for semantic search and distributed memory services.")
    print("Type 'quit', 'exit', or 'bye' to end our conversation.")
    print("Type 'info' to see patient information.")
    print("Type 'context' to see memory context.")
    print("-" * 50)
    
    try:
        chatbot = MCPCLIChatbot()
    except Exception as e:
        print(f"❌ Failed to initialize: {e}")
        return
    
    try:
        while True:
            try:
                # Show patient info if available
                if chatbot.has_name:
                    chatbot.display_patient_info()
                
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye', 'goodbye']:
                    print(f"\n🩺 Assistant: Thank you for chatting with me{', ' + chatbot.patient_name if chatbot.has_name else ''}! Take care!")
                    
                    summary = chatbot.get_session_summary()
                    print(f"\n📊 MCP Session Summary:")
                    print(f"Patient: {summary['patient_name'] if summary['has_name'] else 'Name not provided'}")
                    print(f"Total exchanges: {summary['total_exchanges']}")
                    print(f"MCP enabled: {summary['mcp_enabled']}")
                    print(f"Memory systems: {', '.join(summary['memory_systems_used'])}")
                    break
                
                if user_input.lower() == 'info':
                    chatbot.display_patient_info()
                    continue
                
                if user_input.lower() == 'context':
                    if chatbot.has_name:
                        print("\n🧠 Getting patient context from MCP services...")
                        context = await chatbot.get_patient_context()
                        print(f"\n📋 Patient Context:\n{context}")
                    else:
                        print("\n⚠️  No patient name available yet.")
                    continue
                
                if not user_input:
                    continue
                
                print("\n🔗 Processing with MCP services...")
                response = await chatbot.process_message(user_input)
                print(f"\n🩺 Assistant: {response}")
                
            except KeyboardInterrupt:
                print("\n\nGoodbye!")
                break
            except Exception as e:
                print(f"\nSorry, I encountered an error: {e}")
                print("Please try again.")
    
    finally:
        # Cleanup MCP client
        await chatbot.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
