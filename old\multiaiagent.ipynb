{"cells": [{"cell_type": "markdown", "id": "423c37ca", "metadata": {}, "source": ["## Simple MultiAI Agent Architecture"]}, {"cell_type": "code", "execution_count": 2, "id": "67793a4d", "metadata": {}, "outputs": [], "source": ["import os\n", "from typing import TypedDict, Annotated, List, Literal\n", "from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage\n", "from langchain_groq import ChatGroq\n", "from langchain_core.tools import tool\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "from langgraph.graph import StateGraph, END\n", "from langgraph.prebuilt import create_react_agent\n", "from langgraph.checkpoint.memory import MemorySaver"]}, {"cell_type": "code", "execution_count": 3, "id": "382fefe0", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "os.environ[\"GROQ_API_KEY\"]=os.getenv(\"GROQ_API_KEY\")"]}, {"cell_type": "code", "execution_count": 4, "id": "f1ffca07", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, END, MessagesState\n", "from langgraph.prebuilt import ToolNode\n", "from langgraph.checkpoint.memory import MemorySaver"]}, {"cell_type": "code", "execution_count": null, "id": "d58e671c", "metadata": {}, "outputs": [], "source": ["## Define the state\n", "class AgentState(MessagesState):\n", "    next_agent:str #ehich agent should go next "]}, {"cell_type": "code", "execution_count": 7, "id": "93c5f09f", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x000001A781376510>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x000001A7813770E0>, model_name='llama-3.1-8b-instant', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chat_models import init_chat_model\n", "\n", "llm=init_chat_model(\"groq:llama-3.1-8b-instant\")\n", "llm"]}, {"cell_type": "markdown", "id": "27b0f6e0", "metadata": {}, "source": ["## supervise Multi Ai Agent Architecture"]}, {"cell_type": "code", "execution_count": 16, "id": "3775e90d", "metadata": {}, "outputs": [], "source": ["from typing import TypedDict, Annotated, List, Literal, Dict, Any\n", "from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage\n", "from langgraph.graph import StateGraph, END, MessagesState\n", "from langgraph.checkpoint.memory import MemorySaver\n", "import random\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 18, "id": "1c683177", "metadata": {}, "outputs": [], "source": ["# ===================================\n", "# State Definition\n", "# ===================================\n", "\n", "# ===================================\n", "# State Definition\n", "# ===================================\n", "\n", "class SupervisorState(MessagesState):\n", "    \"\"\"State for the multi-agent system\"\"\"\n", "    next_agent: str = \"\"\n", "    research_data: str = \"\"\n", "    analysis: str = \"\"\n", "    final_report: str = \"\"\n", "    task_complete: bool = False\n", "    current_task: str = \"\""]}, {"cell_type": "code", "execution_count": 28, "id": "47e01561", "metadata": {}, "outputs": [], "source": ["# ===================================\n", "# Supervisor with Groq LLM\n", "# ===================================\n", "from langchain_core.prompts import ChatPromptTemplate\n", "def create_supervisor_chain():\n", "    \"\"\"Creates the supervisor decision chain\"\"\"\n", "    \n", "    supervisor_prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", \"\"\"You are a supervisor managing a team of agents:\n", "        \n", "1. <PERSON><PERSON> <PERSON> Gathers information and data\n", "2. Analyst - Analyzes data and provides insights  \n", "3. Writer - Creates reports and summaries\n", "\n", "Based on the current state and conversation, decide which agent should work next.\n", "If the task is complete, respond with 'DONE'.\n", "\n", "Current state:\n", "- Has research data: {has_research}\n", "- Has analysis: {has_analysis}\n", "- Has report: {has_report}\n", "\n", "Respond with ONLY the agent name (researcher/analyst/writer) or '<PERSON>ON<PERSON>'.\n", "\"\"\"),\n", "        (\"human\", \"{task}\")\n", "    ])\n", "    \n", "    return supervisor_prompt | llm"]}, {"cell_type": "code", "execution_count": 29, "id": "847ed077", "metadata": {}, "outputs": [], "source": ["def supervisor_agent(state: SupervisorState) -> Dict:\n", "    \"\"\"Supervisor decides next agent using Groq LLM\"\"\"\n", "    \n", "    messages = state[\"messages\"]\n", "    task = messages[-1].content if messages else \"No task\"\n", "    \n", "    # Check what's been completed\n", "    has_research = bool(state.get(\"research_data\", \"\"))\n", "    has_analysis = bool(state.get(\"analysis\", \"\"))\n", "    has_report = bool(state.get(\"final_report\", \"\"))\n", "    \n", "    # Get LLM decision\n", "    chain = create_supervisor_chain()\n", "    decision = chain.invoke({\n", "        \"task\": task,\n", "        \"has_research\": has_research,\n", "        \"has_analysis\": has_analysis,\n", "        \"has_report\": has_report\n", "    })\n", "    \n", "    # Parse decision\n", "    decision_text = decision.content.strip().lower()\n", "    print(decision_text)\n", "    \n", "    # Determine next agent\n", "    if \"done\" in decision_text or has_report:\n", "        next_agent = \"end\"\n", "        supervisor_msg = \"✅ Supervisor: All tasks complete! Great work team.\"\n", "    elif \"researcher\" in decision_text or not has_research:\n", "        next_agent = \"researcher\"\n", "        supervisor_msg = \"📋 Supervisor: Let's start with research. Assigning to Researcher...\"\n", "    elif \"analyst\" in decision_text or (has_research and not has_analysis):\n", "        next_agent = \"analyst\"\n", "        supervisor_msg = \"📋 Supervisor: Research done. Time for analysis. Assigning to Analyst...\"\n", "    elif \"writer\" in decision_text or (has_analysis and not has_report):\n", "        next_agent = \"writer\"\n", "        supervisor_msg = \"📋 Supervisor: Analysis complete. Let's create the report. Assigning to Writer...\"\n", "    else:\n", "        next_agent = \"end\"\n", "        supervisor_msg = \"✅ Supervisor: Task seems complete.\"\n", "    \n", "    return {\n", "        \"messages\": [AIMessage(content=supervisor_msg)],\n", "        \"next_agent\": next_agent,\n", "        \"current_task\": task\n", "    }"]}, {"cell_type": "code", "execution_count": 30, "id": "e2491f6f", "metadata": {}, "outputs": [], "source": ["# ===================================\n", "# Agent 1: Researcher (using Groq)\n", "# ===================================\n", "\n", "def researcher_agent(state: SupervisorState) -> Dict:\n", "    \"\"\"Research<PERSON> uses Groq to gather information\"\"\"\n", "    \n", "    task = state.get(\"current_task\", \"research topic\")\n", "    \n", "    # Create research prompt\n", "    research_prompt = f\"\"\"As a research specialist, provide comprehensive information about: {task}\n", "\n", "    Include:\n", "    1. Key facts and background\n", "    2. Current trends or developments\n", "    3. Important statistics or data points\n", "    4. Notable examples or case studies\n", "    \n", "    Be concise but thorough.\"\"\"\n", "    \n", "    # Get research from LLM\n", "    research_response = llm.invoke([HumanMessage(content=research_prompt)])\n", "    research_data = research_response.content\n", "    \n", "    # Create agent message\n", "    agent_message = f\"🔍 Researcher: I've completed the research on '{task}'.\\n\\nKey findings:\\n{research_data[:500]}...\"\n", "    \n", "    return {\n", "        \"messages\": [AIMessage(content=agent_message)],\n", "        \"research_data\": research_data,\n", "        \"next_agent\": \"supervisor\"\n", "    }"]}, {"cell_type": "code", "execution_count": 31, "id": "1739fcab", "metadata": {}, "outputs": [], "source": ["# ===================================\n", "# Agent 2: Analyst (using Groq)\n", "# ===================================\n", "\n", "def analyst_agent(state: SupervisorState) -> Dict:\n", "    \"\"\"Analy<PERSON> uses Groq to analyze the research\"\"\"\n", "    \n", "    research_data = state.get(\"research_data\", \"\")\n", "    task = state.get(\"current_task\", \"\")\n", "    \n", "    # Create analysis prompt\n", "    analysis_prompt = f\"\"\"As a data analyst, analyze this research data and provide insights:\n", "\n", "Research Data:\n", "{research_data}\n", "\n", "Provide:\n", "1. Key insights and patterns\n", "2. Strategic implications\n", "3. Risks and opportunities\n", "4. Recommendations\n", "\n", "Focus on actionable insights related to: {task}\"\"\"\n", "    \n", "    # Get analysis from LLM\n", "    analysis_response = llm.invoke([HumanMessage(content=analysis_prompt)])\n", "    analysis = analysis_response.content\n", "    \n", "    # Create agent message\n", "    agent_message = f\"📊 Analyst: I've completed the analysis.\\n\\nTop insights:\\n{analysis[:400]}...\"\n", "    \n", "    return {\n", "        \"messages\": [AIMessage(content=agent_message)],\n", "        \"analysis\": analysis,\n", "        \"next_agent\": \"supervisor\"\n", "    }"]}, {"cell_type": "code", "execution_count": 32, "id": "94fdc66d", "metadata": {}, "outputs": [], "source": ["# ===================================\n", "# Agent 3: Writer (using Groq)\n", "# ===================================\n", "\n", "def writer_agent(state: SupervisorState) -> Dict:\n", "    \"\"\"Writer uses Groq to create final report\"\"\"\n", "    \n", "    research_data = state.get(\"research_data\", \"\")\n", "    analysis = state.get(\"analysis\", \"\")\n", "    task = state.get(\"current_task\", \"\")\n", "    \n", "    # Create writing prompt\n", "    writing_prompt = f\"\"\"As a professional writer, create an executive report based on:\n", "\n", "Task: {task}\n", "\n", "Research Findings:\n", "{research_data[:1000]}\n", "\n", "Analysis:\n", "{analysis[:1000]}\n", "\n", "Create a well-structured report with:\n", "1. Executive Summary\n", "2. Key Findings  \n", "3. Analysis & Insights\n", "4. Recommendations\n", "5. Conclusion\n", "\n", "Keep it professional and concise.\"\"\"\n", "    \n", "    # Get report from LLM\n", "    report_response = llm.invoke([HumanMessage(content=writing_prompt)])\n", "    report = report_response.content\n", "    \n", "    # Create final formatted report\n", "    final_report = f\"\"\"\n", "📄 FINAL REPORT\n", "{'='*50}\n", "Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n", "Topic: {task}\n", "{'='*50}\n", "\n", "{report}\n", "\n", "{'='*50}\n", "Report compiled by Multi-Agent AI System powered by Groq\n", "\"\"\"\n", "    \n", "    return {\n", "        \"messages\": [AIMessage(content=f\"✍️ Writer: Report complete! See below for the full document.\")],\n", "        \"final_report\": final_report,\n", "        \"next_agent\": \"supervisor\",\n", "        \"task_complete\": True\n", "    }"]}, {"cell_type": "code", "execution_count": 33, "id": "6c6b666c", "metadata": {}, "outputs": [], "source": ["# ===================================\n", "# Router Function\n", "# ===================================\n", "\n", "def router(state: SupervisorState) -> Literal[\"supervisor\", \"researcher\", \"analyst\", \"writer\", \"__end__\"]:\n", "    \"\"\"Routes to next agent based on state\"\"\"\n", "    \n", "    next_agent = state.get(\"next_agent\", \"supervisor\")\n", "    \n", "    if next_agent == \"end\" or state.get(\"task_complete\", False):\n", "        return END\n", "        \n", "    if next_agent in [\"supervisor\", \"researcher\", \"analyst\", \"writer\"]:\n", "        return next_agent\n", "        \n", "    return \"supervisor\""]}, {"cell_type": "code", "execution_count": 34, "id": "d0d36cde", "metadata": {}, "outputs": [], "source": ["# Create workflow\n", "workflow = StateGraph(SupervisorState)\n", "\n", "# Add nodes\n", "workflow.add_node(\"supervisor\", supervisor_agent)\n", "workflow.add_node(\"researcher\", researcher_agent)\n", "workflow.add_node(\"analyst\", analyst_agent)\n", "workflow.add_node(\"writer\", writer_agent)\n", "\n", "# Set entry point\n", "workflow.set_entry_point(\"supervisor\")\n", "\n", "# Add routing\n", "for node in [\"supervisor\", \"researcher\", \"analyst\", \"writer\"]:\n", "    workflow.add_conditional_edges(\n", "        node,\n", "        router,\n", "        {\n", "            \"supervisor\": \"supervisor\",\n", "            \"researcher\": \"researcher\",\n", "            \"analyst\": \"analyst\",\n", "            \"writer\": \"writer\",\n", "            END: END\n", "        }\n", "    )\n", "\n", "graph=workflow.compile()\n", "    "]}, {"cell_type": "code", "execution_count": 35, "id": "41273f6c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x000001A781EFE350>"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["graph"]}, {"cell_type": "code", "execution_count": 37, "id": "aa785aa1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["researcher\n", "analyst\n", "writer\n"]}], "source": ["response=graph.invoke(HumanMessage(content=\"What are the benefits and risks of AI in healthcare?\"))"]}, {"cell_type": "code", "execution_count": 39, "id": "bfe270c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\n📄 FINAL REPORT\\n==================================================\\nGenerated: 2025-07-02 18:08\\nTopic: 📊 Analyst: I\\'ve completed the analysis.\\n\\nTop insights:\\n**Analysis and Insights on \"No Task\"**\\n\\nGiven the lack of a specific topic, I\\'ll provide a general analysis of the provided research template and offer insights on how to approach creating a comprehensive report.\\n\\n**Key Insights and Patterns:**\\n\\n1. **Structure and Template**: The provided template offers a clear and structured approach to creating a comprehensive report on a given topic. It includ...\\n==================================================\\n\\n**Executive Report: \"No Task\" Analysis and Recommendations**\\n\\n**Executive Summary:**\\nIn the absence of a specific topic, this report provides a general analysis and insights on how to approach creating a comprehensive report using the provided research template. The analysis highlights the importance of structure, context, and visual aids in creating a well-rounded report.\\n\\n**Key Findings:**\\n\\n1. **Effective Template Structure**: The provided template offers a clear and structured approach to creating a comprehensive report, including sections on key facts and background, current trends or developments, important statistics or data points, and notable examples or case studies.\\n2. **Importance of Context**: Understanding the background and significance of a topic is crucial for creating a comprehensive report, as it enables readers to appreciate the relevance and impact of the topic.\\n3. **Use of Visual Aids**: Visual aids such as charts, graphs, or infographics can make data more engaging and accessible, enhancing the overall understanding and retention of the report\\'s content.\\n\\n**Analysis & Insights:**\\n\\n1. **Structured Approach**: The template\\'s structured approach ensures that all essential information is addressed, providing a comprehensive and well-rounded report.\\n2. **Contextual Understanding**: Providing historical context, defining key terms and concepts, and highlighting key milestones and developments helps readers understand the topic\\'s significance and relevance.\\n3. **Data Visualization**: Using visual aids can make data more engaging and accessible, facilitating a better understanding of the topic\\'s trends, patterns, and implications.\\n\\n**Recommendations:**\\n\\n1. **Customize the Template**: Adapt the template to suit the specific topic, incorporating relevant sections and eliminating unnecessary ones.\\n2. **Conduct Thorough Research**: Gather comprehensive data and insights to ensure a well-rounded report.\\n3. **Use Visual Aids Effectively**: Utilize charts, graphs, and infographics to make data more engaging and accessible.\\n4. **Ensure Contextual Understanding**: Provide historical context, define key terms and concepts, and highlight key milestones and developments.\\n\\n**Conclusion:**\\nIn conclusion, this report provides a general analysis and insights on how to approach creating a comprehensive report using the provided research template. By following the template\\'s structured approach, understanding the context, and using visual aids effectively, report writers can create well-rounded and engaging reports that cater to their audience\\'s needs.\\n\\n==================================================\\nReport compiled by Multi-Agent AI System powered by Groq\\n'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["response['final_report']"]}, {"cell_type": "markdown", "id": "410df00e", "metadata": {}, "source": ["\"\"\"\n", "Simple Hierarchical Multi-Agent System with Groq\n", "===============================================\n", "Shows how to organize agents in teams with team leaders.\n", "\n", "Structure:\n", "- CEO (top level)\n", "  - Research Team Leader\n", "    - Data Researcher\n", "    - Market Researcher\n", "  - Writing Team Leader\n", "    - Technical Writer\n", "    - Summary Writer"]}, {"cell_type": "code", "execution_count": null, "id": "26a36af9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "AgenticLanggraph", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}